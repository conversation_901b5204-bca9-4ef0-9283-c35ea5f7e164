import pandas as pd
from pprint import pprint
import json

pd.set_option('display.max_columns', None)

from DatabaseManagement.ImportExport import get_table_from_GZ

cases_df = get_table_from_GZ("tb_case", force_refresh=True)

cases_df[cases_df['docket'] == '1:25-cv-08481']

if not cases_df.at[8267, 'aisummary'] or cases_df.at[8267, 'aisummary'] == "":
    print("True")
else:
    print("False")

if cases_df.at[8267, 'aisummary'] is None or cases_df.at[8267, 'aisummary'] == "":
    print("True")
else:
    print("False")

if pd.isna(cases_df.at[8267, 'aisummary']) or cases_df.at[8267, 'aisummary'] == "":
    print("True")

if pd.isna(cases_df.at[idx, 'aisummary']) or cases_df.at[idx, 'aisummary'] == "":

cases_df[cases_df['docket'] == '1:25-cv-08481']

ddff.loc[[8180]]

ddff.loc[[8180]]['images'].values[0]['trademarks']

ids_to_remove = [14416, 14425, 14422, 14420, 14419, 14415, 14421, 14423, 14418, 14417, 14424, 14426]
all_cases_df = all_cases_df[~all_cases_df['id'].isin(ids_to_remove)]

len(all_cases_df)

all_cases_df.head(1)

all_cases_df[all_cases_df['docket'] == '1:25-cv-05113']

fault_case = all_cases_df[all_cases_df['docket'] == '1:25-cv-05113']
fault_case

df = all_cases_df.loc[[8180]]

from Common.Constants import local_case_folder
from Common.Constants import sanitize_name
import os

def process_images_data(images_data):
    """Process images data from different formats"""
    if isinstance(images_data, str):
        return json.loads(images_data)
    return images_data if isinstance(images_data, dict) else {}


def get_case_dir(row):
    """Get case directory path"""
    return os.path.join(
        local_case_folder,
        sanitize_name(f"{row['date_filed'].strftime('%Y-%m-%d')} - {row['docket']}")
    )


def get_image_paths(images, case_dir, plaintiff_id):
    """Generate all image paths and keys"""
    # Reminder of the structure of the field: df["images"]["trademark"][filename]= {regno = "8765468468", full_filename = ["us_indef_pag5"]}
    for ip in images.keys():
        for image in images[ip].keys():
            paths = []
            # Actual images
            for res in ["high", "low"]:
                file_path = os.path.join(case_dir, "images", res, image)
                key = f'plaintiff_images/{int(plaintiff_id)}/{res}/{image}'
                paths.append((key, file_path))

            # Certificates images
            for full_image in images[ip][image]["full_filename"]:
                file_path = os.path.join(case_dir, "images", "high", full_image)
                key = f'plaintiff_images/{int(plaintiff_id)}/high/{full_image}'
                paths.append((key, file_path))

            yield image, paths

upload_tasks_paths = []
for index, row in df.iterrows():
    images = process_images_data(row["images"])
    # print(images)
    # break
    case_dir = get_case_dir(row)
    for image, paths in get_image_paths(images, case_dir, row["plaintiff_id"]):
            for key, file_path in paths:
                upload_tasks_paths.append((file_path, image))

upload_tasks_paths[]

ddff.loc[[8180]]['images'].values[0]['trademarks']['1_25-cv-08010_regno_6169993.webp']

upload_tasks_paths



(all_cases_df.at[8075, 'images_status'] or {})

all_cases_df

all_cases_df.head(10)

all_cases_df.tail(10)







import os
import requests

os.environ["LANGFUSE_SECRET_KEY"] = "******************************************"
os.environ["LANGFUSE_PUBLIC_KEY"] = "pk-lf-bac4b191-590d-4059-89d6-e5ff73e91b48"
os.environ["LANGFUSE_PROJECT_ID"]="cm7i3hojo001rm907nz1tzlea"
os.environ["LANGFUSE_HOST"]="http://***************:3000"

dataset_name = "CopyrightReports"
public_key = os.environ["LANGFUSE_PUBLIC_KEY"]
secret_key = os.environ["LANGFUSE_SECRET_KEY"]
host = os.environ["LANGFUSE_HOST"]

url = f"{host}/api/public/dataset-items"
params = {"datasetName": dataset_name}

response = requests.get(url, params=params, auth=(public_key, secret_key))

if response.status_code == 200:
    data = response.json()
else:
    print(f"Error {response.status_code}: {response.text}")

print(f"Dataset Name: {data['data'][0]['datasetName']}")
print(f"Dataset Id: {data['data'][0]['datasetId']}")    

for i, item in enumerate(data['data'], 1):
    print(f"Total dataset items: {len(data['data'])}")
    print(f"\nItem {i}:")
    print("input:", item.get("input"))
    print("expectedOutput:", item.get("expectedOutput"))
    print("="*100)

dataset_id = "cmcvscloo0004ru07za88db9n"
run_name = "Report_CopyrightSamreen"

url = f"{host}/api/public/datasets/{dataset_name}/runs"
response = requests.get(url, params=params, auth=(public_key, secret_key))

response.json()

dataset_id = "cmcvscloo0004ru07za88db9n"
run_name = "Copyright Experiment - Report_CopyrightSamreen"

url = f"{host}/api/public/dataset-run-items"
params = {
    "datasetId": dataset_id,
    "runName": run_name,
}

response = requests.get(url, params=params, auth=(public_key, secret_key))

response.json()

trace_id = "24adc44ab057082f4b09d0592b588ef1"  # First trace
params = {
    "limit": 1,  # Adjust the limit as needed
    "offset": 0   # Adjust the offset for pagination
}
url = f"{host}/api/public/traces/{trace_id}"

response = requests.get(url, auth=(public_key, secret_key))

response.json()

url = f"{host}/api/public/observations"

response = requests.get(url, auth=(public_key, secret_key))

response.json()

problem_rows = []
for index, row in cases_df.iterrows():
    case_id = row['id']
    images_data = row.get('images', {})

    if not isinstance(images_data, dict):
        # images might be a stringified JSON
        try:
            images_data = json.loads(images_data) if images_data else {}
        except (json.JSONDecodeError, TypeError):
            print(f"Could not parse images data for case {case_id}. Skipping.")
            continue

    trademarks_data = images_data.get('trademarks', {})
    for tm_filename, tm_data in trademarks_data.items():
        reg_nos = tm_data.get('reg_no', [])
        ser_nos = tm_data.get('ser_no', [])

        # Normalize to list
        if not isinstance(reg_nos, list):
            reg_nos = [reg_nos]
        if not isinstance(ser_nos, list):
            ser_nos = [ser_nos]

        # Filter entries that start with '000'
        bad_reg_nos = [r for r in reg_nos if str(r).startswith('000')]
        bad_ser_nos = [s for s in ser_nos if str(s).startswith('000')]

        if bad_reg_nos or bad_ser_nos:
            problem_rows.append({
                'case_id': case_id,
                'tm_filename': tm_filename,
                'bad_reg_nos': bad_reg_nos,
                'bad_ser_nos': bad_ser_nos
            })


len(problem_rows)

problem_df = pd.DataFrame(problem_rows)
problem_df

